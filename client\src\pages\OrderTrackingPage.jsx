import React, { useEffect, useState } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { 
  Steps, 
  Card, 
  Typography, 
  Divider, 
  Alert, 
  Spin, 
  Input, 
  Button, 
  Row, 
  Col, 
  Tag, 
  Timeline,
  Space,
  Progress,
  Badge,
  Descriptions,
  Empty,
  Table,
  Tabs,
  Statistic,
  List,
  Avatar,
  notification
} from 'antd';
import { 
  SearchOutlined, 
  TruckOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
  EyeOutlined,
  ShoppingCartOutlined,
  InboxOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { orderTrackingApi } from '../services/orderTrackingApi';
import { orderApi } from '../services/orderApi';
import { useAuth } from '../hooks/useAuth';
import Header from '../components/Header';
import Footer from '../components/Footer';

const { Step } = Steps;
const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { TabPane } = Tabs;

const OrderTrackingPage = () => {
  const { trackingNumber: urlTrackingNumber } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const orderId = searchParams.get('orderId');
  
  const [trackingData, setTrackingData] = useState(null);
  const [orders, setOrders] = useState([]);
  const [orderStats, setOrderStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchValue, setSearchValue] = useState(urlTrackingNumber || '');
  const [searched, setSearched] = useState(!!urlTrackingNumber);
  const [activeTab, setActiveTab] = useState(urlTrackingNumber ? '2' : '1');

  const statusColors = {
    'order_confirmed': 'blue',
    'processing': 'orange',
    'shipped': 'cyan',
    'out_for_delivery': 'gold',
    'delivered': 'green',
    'cancelled': 'red',
    'returned': 'purple'
  };

  const statusIcons = {
    'order_confirmed': <CheckCircleOutlined />,
    'processing': <ClockCircleOutlined />,
    'shipped': <TruckOutlined />,
    'out_for_delivery': <EnvironmentOutlined />,
    'delivered': <CheckCircleOutlined />,
    'cancelled': <ClockCircleOutlined />,
    'returned': <TruckOutlined />
  };

  useEffect(() => {
    if (urlTrackingNumber) {
      handleSearch(urlTrackingNumber);
    } else if (orderId) {
      fetchTrackingByOrderId(orderId);
    }
    
    // Load orders list if user is authenticated and not viewing specific tracking
    if (isAuthenticated && !urlTrackingNumber && !orderId) {
      fetchOrders();
      fetchOrderStats();
    }
  }, [urlTrackingNumber, orderId, isAuthenticated]);

  const fetchOrders = async () => {
    if (!isAuthenticated) return;
    
    setOrdersLoading(true);
    try {
      const response = await orderApi.getCustomerOrders({ limit: 20 });
      setOrders(response.data.orders || []);
    } catch (err) {
      notification.error({
        message: 'Error',
        description: 'Failed to load orders'
      });
    } finally {
      setOrdersLoading(false);
    }
  };

  const fetchOrderStats = async () => {
    if (!isAuthenticated) return;
    
    try {
      const response = await orderApi.getOrderStats();
      setOrderStats(response.data);
    } catch (err) {
      console.error('Failed to load order stats:', err);
    }
  };

  const fetchTrackingByOrderId = async (orderIdParam) => {
    setLoading(true);
    setError(null);
    try {
      const data = await orderTrackingApi.getTrackingByOrderId(orderIdParam);
      setTrackingData(data.data);
      setSearched(true);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (trackingNum = searchValue) => {
    if (!trackingNum?.trim()) {
      setError('Please enter a tracking number');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const data = await orderTrackingApi.getTrackingByNumber(trackingNum.trim());
      setTrackingData(data.data);
      setSearched(true);
    } catch (err) {
      setError(err.message);
      setTrackingData(null);
    } finally {
      setLoading(false);
    }
  };

  const renderSearchSection = () => (
    <Card className="mb-6">
      <Title level={3} className="text-center mb-4">Track Your Order</Title>
      <Row justify="center">
        <Col xs={24} sm={20} md={16} lg={12}>
          <Space.Compact style={{ width: '100%' }}>
            <Search
              placeholder="Enter tracking number (e.g., TRK123456789ABCD)"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={handleSearch}
              enterButton={
                <Button type="primary" icon={<SearchOutlined />}>
                  Track
                </Button>
              }
              size="large"
            />
          </Space.Compact>
        </Col>
      </Row>
    </Card>
  );

  const renderTrackingInfo = () => {
    if (!trackingData) return null;

    return (
      <>
        {/* Order Summary Card */}
        <Card className="mb-6">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Descriptions title="Order Details" size="small" column={1}>
                <Descriptions.Item label="Tracking Number">
                  <Text strong>{trackingData.trackingNumber}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Order Number">
                  <Text>{trackingData.order?.orderNumber}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Status">
                  <Badge 
                    status={trackingData.currentStatus === 'delivered' ? 'success' : 'processing'} 
                    text={
                      <Tag color={statusColors[trackingData.currentStatus]} icon={statusIcons[trackingData.currentStatus]}>
                        {trackingData.currentStatus.replace('_', ' ').toUpperCase()}
                      </Tag>
                    }
                  />
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Descriptions title="Delivery Info" size="small" column={1}>
                <Descriptions.Item label="Carrier">
                  <Text>{trackingData.carrier?.name}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Estimated Delivery">
                  <Text>
                    {trackingData.estimatedDelivery 
                      ? new Date(trackingData.estimatedDelivery).toLocaleDateString()
                      : 'TBD'
                    }
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="Vendor">
                  <Text>{trackingData.vendor?.businessName}</Text>
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col xs={24} sm={24} md={8}>
              <div className="text-center">
                <Title level={4}>Progress</Title>
                <Progress 
                  type="circle" 
                  percent={trackingData.progressPercentage || 0}
                  status={trackingData.currentStatus === 'delivered' ? 'success' : 'active'}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>
            </Col>
          </Row>
        </Card>

        {/* Tracking Steps */}
        <Card title="Tracking Timeline">
          <Steps 
            direction="vertical" 
            current={trackingData.currentStepIndex >= 0 ? trackingData.currentStepIndex : 0}
            status={trackingData.currentStatus === 'cancelled' ? 'error' : 'process'}
          >
            {trackingData.trackingSteps.map((step, index) => {
              const isCurrentStep = index === trackingData.currentStepIndex;
              const isPastStep = index < trackingData.currentStepIndex;
              
              return (
                <Step
                  key={index}
                  title={
                    <div>
                      <Text strong={isCurrentStep}>{step.title}</Text>
                      {isCurrentStep && (
                        <Tag color="blue" style={{ marginLeft: 8 }}>Current</Tag>
                      )}
                    </div>
                  }
                  description={
                    <div>
                      <Paragraph className="mb-2">{step.description}</Paragraph>
                      <div className="text-secondary">
                        <Text type="secondary">
                          <ClockCircleOutlined className="mr-1" />
                          {new Date(step.timestamp).toLocaleString()}
                        </Text>
                      </div>
                      {step.location && (
                        <div className="mt-2">
                          <Text type="secondary">
                            <EnvironmentOutlined className="mr-1" />
                            {[step.location.city, step.location.state, step.location.country]
                              .filter(Boolean).join(', ')}
                          </Text>
                        </div>
                      )}
                      {step.updatedBy && step.updatedBy.type !== 'system' && (
                        <div className="mt-1">
                          <Text type="secondary" className="text-xs">
                            Updated by: {step.updatedBy.type}
                          </Text>
                        </div>
                      )}
                    </div>
                  }
                  status={
                    isPastStep ? 'finish' : 
                    isCurrentStep ? 'process' : 'wait'
                  }
                />
              );
            })}
          </Steps>
        </Card>

        {/* Contact Information */}
        {trackingData.carrier?.contactInfo && (
          <Card title="Carrier Contact" className="mt-6">
            <Row gutter={[16, 16]}>
              {trackingData.carrier.contactInfo.phone && (
                <Col xs={24} sm={8}>
                  <Space>
                    <PhoneOutlined />
                    <Text>{trackingData.carrier.contactInfo.phone}</Text>
                  </Space>
                </Col>
              )}
              {trackingData.carrier.contactInfo.email && (
                <Col xs={24} sm={8}>
                  <Space>
                    <MailOutlined />
                    <Text>{trackingData.carrier.contactInfo.email}</Text>
                  </Space>
                </Col>
              )}
              {trackingData.carrier.contactInfo.website && (
                <Col xs={24} sm={8}>
                  <a href={trackingData.carrier.contactInfo.website} target="_blank" rel="noopener noreferrer">
                    Visit Carrier Website
                  </a>
                </Col>
              )}
            </Row>
          </Card>
        )}

        {/* Notes Section */}
        {trackingData.notes && trackingData.notes.length > 0 && (
          <Card title="Notes" className="mt-6">
            <Timeline>
              {trackingData.notes.map((note, index) => (
                <Timeline.Item key={index}>
                  <div>
                    <Text>{note.message}</Text>
                    <br />
                    <Text type="secondary" className="text-xs">
                      {new Date(note.timestamp).toLocaleString()} - {note.addedBy?.type || 'System'}
                    </Text>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        )}
      </>
    );
  };

  const renderNoResults = () => (
    <Card>
      <Empty
        description="No tracking information found"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      >
        <Text type="secondary">
          Please check your tracking number and try again.
        </Text>
      </Empty>
    </Card>
  );

  const renderOrderStats = () => {
    if (!orderStats) return null;

    return (
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Total Orders"
              value={orderStats.totalOrders}
              prefix={<ShoppingCartOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Delivered"
              value={orderStats.deliveredOrders}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="In Progress"
              value={orderStats.processingOrders + orderStats.shippedOrders}
              prefix={<TruckOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Total Spent"
              value={orderStats.totalSpent}
              prefix="₹"
              precision={0}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  const renderOrdersList = () => {
    if (ordersLoading) {
      return (
        <Card>
          <div className="text-center py-8">
            <Spin size="large" />
            <div className="mt-2">
              <Text>Loading your orders...</Text>
            </div>
          </div>
        </Card>
      );
    }

    if (!orders.length) {
      return (
        <Card>
          <Empty
            description="No orders found"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Text type="secondary">
              You haven't placed any orders yet.
            </Text>
            <br />
            <Button 
              type="primary" 
              onClick={() => navigate('/')}
              className="mt-4"
            >
              Start Shopping
            </Button>
          </Empty>
        </Card>
      );
    }

    return (
      <List
        itemLayout="vertical"
        dataSource={orders}
        renderItem={(order) => (
          <List.Item
            key={order._id}
            actions={[
              order.tracking?.length > 0 && (
                <Button
                  type="link"
                  icon={<EyeOutlined />}
                  onClick={() => navigate(`/track-order/${order.tracking[0].trackingNumber}`)}
                >
                  Track Order
                </Button>
              ),
              <Button
                type="link"
                icon={<HistoryOutlined />}
                onClick={() => navigate(`/orders?orderId=${order._id}`)}
              >
                View Details
              </Button>
            ].filter(Boolean)}
          >
            <Card>
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={16}>
                  <div>
                    <Title level={5} className="mb-2">
                      Order #{order.orderNumber}
                    </Title>
                    <div className="mb-3">
                      <Tag color={statusColors[order.status]} icon={statusIcons[order.status]}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </Tag>
                      <Text type="secondary" className="ml-2">
                        {new Date(order.createdAt).toLocaleDateString()}
                      </Text>
                    </div>
                    <div className="mb-2">
                      <Text strong>Items: </Text>
                      <Text>{order.items?.length || 0} items</Text>
                    </div>
                    {order.tracking?.length > 0 && (
                      <div>
                        <Text strong>Tracking: </Text>
                        {order.tracking.map((track, index) => (
                          <Tag key={index} className="mr-1">
                            {track.trackingNumber}
                          </Tag>
                        ))}
                      </div>
                    )}
                  </div>
                </Col>
                <Col xs={24} sm={8} className="text-right">
                  <div>
                    <Title level={4} type="success">
                      ₹{order.pricing?.total || 0}
                    </Title>
                    {order.tracking?.length > 0 && (
                      <div className="mt-2">
                        <Progress
                          percent={order.tracking[0].progressPercentage || 0}
                          size="small"
                          status={order.status === 'delivered' ? 'success' : 'active'}
                        />
                        <Text type="secondary" className="text-xs">
                          {order.tracking[0].currentStatus?.replace('_', ' ')?.toUpperCase()}
                        </Text>
                      </div>
                    )}
                  </div>
                </Col>
              </Row>
            </Card>
          </List.Item>
        )}
      />
    );
  };

  const renderTabsContent = () => {
    if (!isAuthenticated) {
      return (
        <Card>
          <Empty
            description="Please login to view your orders"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button type="primary" onClick={() => navigate('/auth')}>
              Login
            </Button>
          </Empty>
        </Card>
      );
    }

    return (
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={
            <span>
              <InboxOutlined />
              Your Orders
            </span>
          } 
          key="1"
        >
          {renderOrderStats()}
          {renderOrdersList()}
        </TabPane>
        <TabPane 
          tab={
            <span>
              <SearchOutlined />
              Track by Number
            </span>
          } 
          key="2"
        >
          <div className="py-4">
            {loading && (
              <div className="text-center py-8">
                <Spin size="large" />
                <div className="mt-2">
                  <Text>Loading tracking information...</Text>
                </div>
              </div>
            )}
            
            {error && (
              <Alert 
                message="Error" 
                description={error} 
                type="error" 
                showIcon 
                className="mb-6"
              />
            )}
            
            {!loading && searched && !error && trackingData && renderTrackingInfo()}
            {!loading && searched && !error && !trackingData && renderNoResults()}
          </div>
        </TabPane>
      </Tabs>
    );
  };

  // If specific tracking number or order ID is provided, show only tracking info
  if (urlTrackingNumber || orderId) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8">
          {renderSearchSection()}
          
          {loading && (
            <div className="text-center py-8">
              <Spin size="large" />
              <div className="mt-2">
                <Text>Loading tracking information...</Text>
              </div>
            </div>
          )}
          
          {error && (
            <Alert 
              message="Error" 
              description={error} 
              type="error" 
              showIcon 
              className="mb-6"
            />
          )}
          
          {!loading && searched && !error && trackingData && renderTrackingInfo()}
          {!loading && searched && !error && !trackingData && renderNoResults()}
        </div>
        <Footer />
      </div>
    );
  }

  // Default view with tabs for order list and tracking search
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <Title level={2}>Order Tracking & Management</Title>
          <Paragraph type="secondary">
            Track your orders or search by tracking number
          </Paragraph>
        </div>
        
        {renderTabsContent()}
      </div>
      <Footer />
    </div>
  );
};

export default OrderTrackingPage;