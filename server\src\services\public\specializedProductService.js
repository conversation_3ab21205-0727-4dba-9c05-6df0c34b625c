const Product = require('../../models/Product');
const Category = require('../../models/Category');
const Vendor = require('../../models/Vendor');

/**
 * Get featured products
 */
const getFeaturedProducts = async (limit = 8) => {
  return await Product.find({
    status: 'active',
    visibility: 'public',
    featured: true
  })
    .populate('vendor', 'businessName')
    .populate('category', 'name slug')
    .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating sales.totalSold')
    .sort({ 'sales.totalSold': -1, createdAt: -1 })
    .limit(limit)
    .lean();
};

/**
 * Get new arrival products
 */
const getNewArrivals = async (limit = 8, days = 30) => {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return await Product.find({
    status: 'active',
    visibility: 'public',
    createdAt: { $gte: startDate }
  })
    .populate('vendor', 'businessName')
    .populate('category', 'name slug')
    .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating createdAt')
    .sort({ createdAt: -1 })
    .limit(limit)
    .lean();
};

/**
 * Get best selling products
 */
const getBestSellingProducts = async (limit = 8) => {
  return await Product.find({
    status: 'active',
    visibility: 'public',
    'sales.totalSold': { $gt: 0 }
  })
    .populate('vendor', 'businessName')
    .populate('category', 'name slug')
    .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating sales.totalSold')
    .sort({ 'sales.totalSold': -1 })
    .limit(limit)
    .lean();
};

/**
 * Get products by category with descendant categories
 */
const getProductsByCategory = async (categoryId, queryParams) => {
  const page = parseInt(queryParams.page) || 1;
  const limit = parseInt(queryParams.limit) || 12;
  const skip = (page - 1) * limit;
  const sortBy = queryParams.sortBy || 'createdAt';
  const sortOrder = queryParams.sortOrder === 'asc' ? 1 : -1;

  // Verify category exists
  const category = await Category.findOne({ 
    _id: categoryId, 
    status: 'active' 
  });

  if (!category) {
    throw new Error('Category not found');
  }

  // Get all descendant categories
  const descendants = await category.getDescendants();
  const categoryIds = [categoryId, ...descendants.map(d => d._id)];

  // Build sort object
  const sort = {};
  if (sortBy === 'price') {
    sort['pricing.basePrice'] = sortOrder;
  } else if (sortBy === 'rating') {
    sort['reviews.averageRating'] = sortOrder;
  } else if (sortBy === 'popularity') {
    sort['sales.totalSold'] = sortOrder;
  } else {
    sort[sortBy] = sortOrder;
  }

  const [products, totalProducts] = await Promise.all([
    Product.find({
      category: { $in: categoryIds },
      status: 'active',
      visibility: 'public'
    })
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean(),
    Product.countDocuments({
      category: { $in: categoryIds },
      status: 'active',
      visibility: 'public'
    })
  ]);

  const totalPages = Math.ceil(totalProducts / limit);

  return {
    category: {
      _id: category._id,
      name: category.name,
      slug: category.slug,
      description: category.description
    },
    products,
    pagination: {
      currentPage: page,
      totalPages,
      totalProducts,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      limit
    }
  };
};

/**
 * Get products by vendor
 */
const getProductsByVendor = async (vendorId, queryParams) => {
  const page = parseInt(queryParams.page) || 1;
  const limit = parseInt(queryParams.limit) || 12;
  const skip = (page - 1) * limit;

  // Verify vendor exists and is active
  const vendor = await Vendor.findOne({ 
    _id: vendorId, 
    status: 'active',
    'verification.status': 'verified'
  }).populate('user', 'firstName lastName');

  if (!vendor) {
    throw new Error('Vendor not found');
  }

  const [products, totalProducts] = await Promise.all([
    Product.find({
      vendor: vendorId,
      status: 'active',
      visibility: 'public'
    })
      .populate('category', 'name slug')
      .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    Product.countDocuments({
      vendor: vendorId,
      status: 'active',
      visibility: 'public'
    })
  ]);

  const totalPages = Math.ceil(totalProducts / limit);

  return {
    vendor: {
      _id: vendor._id,
      businessName: vendor.businessName,
      businessDescription: vendor.businessDescription,
      logo: vendor.logo,
      performance: vendor.performance
    },
    products,
    pagination: {
      currentPage: page,
      totalPages,
      totalProducts,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      limit
    }
  };
};

/**
 * Search products
 */
const searchProducts = async (query, queryParams) => {
  const page = parseInt(queryParams.page) || 1;
  const limit = parseInt(queryParams.limit) || 12;
  const skip = (page - 1) * limit;

  if (!query || query.trim().length < 2) {
    throw new Error('Search query must be at least 2 characters long');
  }

  // Build search filter
  const searchFilter = {
    status: 'active',
    visibility: 'public',
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { description: { $regex: query, $options: 'i' } },
      { tags: { $in: [new RegExp(query, 'i')] } },
      { brand: { $regex: query, $options: 'i' } }
    ]
  };

  const [products, totalProducts] = await Promise.all([
    Product.find(searchFilter)
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating')
      .sort({ 'sales.totalSold': -1, 'reviews.averageRating': -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    Product.countDocuments(searchFilter)
  ]);

  const totalPages = Math.ceil(totalProducts / limit);

  return {
    query,
    products,
    pagination: {
      currentPage: page,
      totalPages,
      totalProducts,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      limit
    }
  };
};

module.exports = {
  getFeaturedProducts,
  getNewArrivals,
  getBestSellingProducts,
  getProductsByCategory,
  getProductsByVendor,
  searchProducts
};
