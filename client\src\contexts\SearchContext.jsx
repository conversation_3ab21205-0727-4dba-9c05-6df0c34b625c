import React, { createContext, useContext, useState, useCallback } from 'react';
import { message } from 'antd';
import { productsApi, searchApi } from '../services/publicApi';
import { useCurrency } from './CurrencyContext';

const SearchContext = createContext();

export const useSearch = () => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
};

export const SearchProvider = ({ children }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filters, setFilters] = useState({
    sortBy: 'relevance', // relevance, price-low-high, price-high-low, rating, newest
    category: '',
    minPrice: '',
    maxPrice: '',
    inStock: false
  });

  // Search products
  const searchProducts = useCallback(async (query, customFilters = {}) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setLoading(true);
      const searchFilters = { ...filters, ...customFilters };
      
      // Build search parameters
      const params = {
        limit: 50,
        ...searchFilters
      };

      // Remove empty values
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      const response = await productsApi.searchProducts(query, params);
      
      if (response.data.success) {
        let results = response.data.data.products || response.data.data || [];
        
        // Apply client-side sorting if needed
        results = applySorting(results, searchFilters.sortBy);
        
        setSearchResults(results);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      
      // More detailed error logging
      if (error.code === 'ERR_NETWORK') {
        console.error('Network error - check if server is running on http://localhost:5000');
        message.error('Network error: Unable to connect to server. Please check if the server is running.');
      } else if (error.response?.status === 0) {
        console.error('CORS error - preflight request failed');
        message.error('CORS error: Cross-origin request blocked. Please check server CORS configuration.');
      } else if (error.response) {
        console.error('Server responded with error:', error.response.status, error.response.data);
        message.error(`Server error: ${error.response.status} - ${error.response.statusText}`);
      } else {
        console.error('Unknown error:', error.message);
        message.error('Failed to search products');
      }
      
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Get search suggestions
  const getSuggestions = useCallback(async (query) => {
    if (!query.trim() || query.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      const response = await searchApi.getSuggestions(query);
      if (response.data.success) {
        setSuggestions(response.data.data || []);
      }
    } catch (error) {
      console.error('Suggestions error:', error);
      setSuggestions([]);
    }
  }, []);

  // Apply sorting to results
  const applySorting = (products, sortBy) => {
    const sortedProducts = [...products];
    
    switch (sortBy) {
      case 'price-low-high':
        return sortedProducts.sort((a, b) => {
          const priceA = a.pricing?.salePrice || a.pricing?.basePrice || 0;
          const priceB = b.pricing?.salePrice || b.pricing?.basePrice || 0;
          return priceA - priceB;
        });
      
      case 'price-high-low':
        return sortedProducts.sort((a, b) => {
          const priceA = a.pricing?.salePrice || a.pricing?.basePrice || 0;
          const priceB = b.pricing?.salePrice || b.pricing?.basePrice || 0;
          return priceB - priceA;
        });
      
      case 'rating':
        return sortedProducts.sort((a, b) => {
          const ratingA = a.reviews?.averageRating || 0;
          const ratingB = b.reviews?.averageRating || 0;
          return ratingB - ratingA;
        });
      
      case 'newest':
        return sortedProducts.sort((a, b) => {
          return new Date(b.createdAt) - new Date(a.createdAt);
        });
      
      default: // relevance
        return sortedProducts;
    }
  };

  // Update filters
  const updateFilters = useCallback((newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setSearchResults([]);
    setSuggestions([]);
    setShowSuggestions(false);
    setFilters({
      sortBy: 'relevance',
      category: '',
      minPrice: '',
      maxPrice: '',
      inStock: false
    });
  }, []);

  const value = {
    // State
    searchTerm,
    searchResults,
    loading,
    suggestions,
    showSuggestions,
    filters,
    
    // Actions
    setSearchTerm,
    setShowSuggestions,
    searchProducts,
    getSuggestions,
    updateFilters,
    clearSearch
  };

  return (
    <SearchContext.Provider value={value}>
      {children}
    </SearchContext.Provider>
  );
};
