import React, { useState, useEffect } from 'react';
import { Card, Modal, Spin, Alert } from 'antd';
import { StarOutlined, GiftOutlined, AudioOutlined, CrownOutlined, ContactsOutlined, HomeOutlined, CloseOutlined, AppstoreOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { ProductInfo } from '../../utils/Api';
import { categoriesApi } from '../../services/publicApi';
import { getHomepageData } from '../../services/homepageApi';

const CatergoriesMenu = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [categoriesError, setCategoriesError] = useState(null);
  const navigate = useNavigate();

  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
  }, []);

  // Fetch products when modal opens
  useEffect(() => {
    if (isModalOpen) {
      setLoading(true);
      ProductInfo()
        .then(data => {
          setProducts(data.slice(0, 8)); // Limit to 8 products for display
          setLoading(false);
        })
        .catch(error => {
          console.error('Error fetching products:', error);
          setLoading(false);
        });
    }
  }, [isModalOpen]);

  const fetchCategories = async () => {
    try {
      setCategoriesLoading(true);

      // First try to get featured categories from homepage settings
      try {
        const homepageResponse = await getHomepageData();
        if (homepageResponse.data.success && homepageResponse.data.data.featuredCategories?.length > 0) {
          const featuredCategories = homepageResponse.data.data.featuredCategories.map(cat => ({
            _id: cat.category._id,
            name: cat.displayName || cat.category.name,
            slug: cat.category.slug,
            imageUrl: cat.imageUrl,
            icon: getCategoryIcon(cat.displayName || cat.category.name)
          }));
          setCategories(featuredCategories);
          return;
        }
      } catch (homepageError) {
        console.warn('Failed to fetch featured categories, falling back to regular categories:', homepageError);
      }

      // Fallback to regular categories if no featured categories
      const response = await categoriesApi.getCategories();
      if (response.data.success) {
        // Map categories to include icons and limit to top-level categories
        const mappedCategories = response.data.data.categories
          .filter(cat => !cat.parent) // Only top-level categories
          .slice(0, 7) // Limit to 7 categories
          .map(cat => ({
            ...cat,
            icon: getCategoryIcon(cat.name)
          }));
        setCategories(mappedCategories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategoriesError('Failed to load categories');
    } finally {
      setCategoriesLoading(false);
    }
  };

  const getCategoryIcon = (categoryName) => {
    const name = categoryName.toLowerCase();
    if (name.includes('electronic') || name.includes('tech')) return <AudioOutlined />;
    if (name.includes('home') || name.includes('garden')) return <HomeOutlined />;
    if (name.includes('jewelry') || name.includes('watch') || name.includes('fashion')) return <CrownOutlined />;
    if (name.includes('apparel') || name.includes('clothing') || name.includes('accessories')) return <ContactsOutlined />;
    if (name.includes('craft') || name.includes('gift') || name.includes('decor')) return <GiftOutlined />;
    return <AppstoreOutlined />; // Default icon
  };

  const handleCategoryClick = (category) => {
    setSelectedCategory(category);
    setIsModalOpen(true);
  };

  const handleProductClick = (productId) => {
    navigate(`/product/${productId}`);
    setIsModalOpen(false);
  };

  if (categoriesLoading) {
    return (
      <div className="w-full max-w-full h-full">
        <Card className="w-full sm:w-64 md:w-56 lg:w-72 xl:w-80 max-w-full p-0 border-none shadow-none bg-transparent h-full flex items-center justify-center">
          <Spin size="large" />
        </Card>
      </div>
    );
  }

  if (categoriesError || categories.length === 0) {
    return (
      <div className="w-full max-w-full h-full">
        <Card className="w-full sm:w-64 md:w-56 lg:w-72 xl:w-80 max-w-full p-0 border-none shadow-none bg-transparent h-full">
          <Alert
            message="Categories unavailable"
            description="Unable to load categories at this time."
            type="info"
            showIcon
            size="small"
          />
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-full h-full">
      <Card className="w-full sm:w-64 md:w-56 lg:w-72 xl:w-80 max-w-full p-0 border-none shadow-none bg-transparent h-full">
        <ul className="space-y-2">
          {categories.map((category, index) => (
            <li
              key={category._id || index}
              className="flex items-center justify-between cursor-pointer hover:bg-gray-100 rounded px-2 py-1"
              onClick={() => handleCategoryClick(category)}
            >
              <span className="flex items-center gap-2 text-gray-700">{category.icon} {category.name}</span>
              <span className="text-gray-400">&gt;</span>
            </li>
          ))}
        </ul>
      </Card>

      <Modal
        title={selectedCategory?.name || "Category Products"}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width={800}
        closeIcon={<CloseOutlined />}
      >
        <div className="flex flex-col md:flex-row">
          {/* Left side - Categories */}
          <div className="w-full md:w-1/3 border-r border-gray-200 pr-4">
            <ul className="space-y-2">
              {categories.map((category, index) => (
                <li
                  key={category._id || index}
                  className={`flex items-center cursor-pointer hover:bg-gray-100 rounded px-2 py-2 ${selectedCategory?._id === category._id ? 'bg-blue-50 text-blue-600' : ''}`}
                  onClick={() => setSelectedCategory(category)}
                >
                  <span className="flex items-center gap-2">{category.icon} {category.name}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Right side - Products */}
          <div className="w-full md:w-2/3 pl-4 mt-4 md:mt-0">
            {loading ? (
              <div className="grid grid-cols-2 gap-4">
                {[1, 2, 3, 4].map((_, index) => (
                  <div key={index} className="animate-pulse bg-gray-200 h-40 rounded-lg"></div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                {products.map(product => (
                  <div
                    key={product.id}
                    className="cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-100 rounded-lg overflow-hidden"
                    onClick={() => handleProductClick(product.id)}
                  >
                    <div className="h-32 overflow-hidden">
                      <img
                        src={product.images?.[0] || 'https://via.placeholder.com/200x200?text=No+Image'}
                        alt={product.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.src = 'https://via.placeholder.com/200x200?text=No+Image';
                        }}
                      />
                    </div>
                    <div className="p-2">
                      <p className="text-sm font-medium text-gray-800 truncate">{product.title}</p>
                      <p className="text-sm font-bold text-red-600">${product.price}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CatergoriesMenu;