import React, { useState } from "react";
import { DownOutlined, InfoCircleOutlined, AppstoreOutlined, ShopOutlined, SafetyOutlined, GlobalOutlined, CustomerServiceOutlined, MobileOutlined, MenuOutlined } from "@ant-design/icons";
import { useCurrency } from '../contexts/CurrencyContext';

const Header = () => {
    const { currentCurrency, supportedCurrencies, changeCurrency, getCurrencySymbol, loading } = useCurrency();
    const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);
    const [isCurrencyOpen, setIsCurrencyOpen] = useState(false);
    const [isSourceOpen, setIsSourceOpen] = useState(false);
    const [isSellOpen, setIsSellOpen] = useState(false);
    const [isHelpOpen, setIsHelpOpen] = useState(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    const categories = [
        { name: "Electronics & Technology", icon: "📱" },
        { name: "Fashion & Apparel", icon: "👕" },
        { name: "Home & Garden", icon: "🏠" },
        { name: "Sports & Outdoors", icon: "⚽" },
        { name: "Health & Beauty", icon: "💄" },
        { name: "Automotive", icon: "🚗" },
        { name: "Industrial Equipment", icon: "🏭" },
        { name: "Food & Beverages", icon: "🍕" },
        { name: "Books & Media", icon: "📚" },
        { name: "Toys & Games", icon: "🧸" }
    ];

    const languages = [
        "English - USD",
        "Spanish - EUR",
        "French - EUR",
        "German - EUR",
        "Chinese - CNY",
        "Japanese - JPY",
        "Korean - KRW",
        "Arabic - AED",
        "Portuguese - BRL",
        "Russian - RUB"
    ];

    const sourceOptions = [
        "Request for Quotation",
        "Trade Assurance",
        "Verified Suppliers",
        "Global Trade Services"
    ];

    const sellOptions = [
        "Start Selling",
        "Seller Central",
        "Supplier Membership",
        "Advertising Solutions"
    ];

    const helpOptions = [
        "Customer Service",
        "Dispute Resolution",
        "Report IPR Infringement",
        "Report Abuse",
        "Resource Center"
    ];

    const closeAllModals = () => {
        setIsCategoriesOpen(false);
        setIsCurrencyOpen(false);
        setIsSourceOpen(false);
        setIsSellOpen(false);
        setIsHelpOpen(false);
        setIsMobileMenuOpen(false);
    };

    return (
        <>
            {/* Main Navigation Menu */}
            <div className="bg-white border-b border-gray-200 w-full">
                <div className="container mx-auto px-2 sm:px-4">
                    <div className="flex items-center justify-between h-10 sm:h-12 text-xs sm:text-sm">
                        {/* Desktop Categories Dropdown */}
                        <div className="hidden lg:block relative mr-4 xl:mr-6">
                            <button 
                                className="flex items-center text-gray-700 hover:text-orange-500 font-medium"
                                onClick={() => setIsCategoriesOpen(!isCategoriesOpen)}
                            >
                                <AppstoreOutlined className="mr-1" />
                                <span>Categories</span>
                                <DownOutlined className="ml-1 text-xs" />
                            </button>
                            
                            {/* Categories Dropdown Menu */}
                            {isCategoriesOpen && (
                                <div className="absolute top-full left-0 mt-1 w-56 xl:w-64 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                    <div className="py-2">
                                        {categories.map((category, index) => (
                                            <a
                                                key={index}
                                                href="#"
                                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-orange-500"
                                                onClick={() => setIsCategoriesOpen(false)}
                                            >
                                                <span className="mr-3">{category.icon}</span>
                                                {category.name}
                                            </a>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Desktop Navigation Links */}
                        <div className="hidden lg:flex flex-1 space-x-4 xl:space-x-8 overflow-x-auto hide-scrollbar">
                            <a href="#" className="whitespace-nowrap text-gray-700 hover:text-orange-500 flex items-center">
                                <SafetyOutlined className="mr-1" />
                                <span className="hidden xl:inline">Ready to Ship</span>
                                <span className="xl:hidden">Ship</span>
                            </a>
                            <a href="#" className="whitespace-nowrap text-gray-700 hover:text-orange-500 flex items-center">
                                <GlobalOutlined className="mr-1" />
                                <span className="hidden xl:inline">Trade Shows</span>
                                <span className="xl:hidden">Trade</span>
                            </a>
                            <a href="#" className="whitespace-nowrap text-gray-700 hover:text-orange-500 hidden xl:block">Personal Protective Equipment</a>
                            
                            {/* Source on Alicartify Dropdown */}
                            <div className="relative">
                                <button 
                                    className="whitespace-nowrap text-gray-700 hover:text-orange-500 flex items-center"
                                    onClick={() => setIsSourceOpen(!isSourceOpen)}
                                >
                                    <span className="hidden xl:inline">Source on Alicartify</span>
                                    <span className="xl:hidden">Source</span>
                                    <DownOutlined className="ml-1 text-xs" />
                                </button>
                                
                                {isSourceOpen && (
                                    <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                        <div className="py-2">
                                            {sourceOptions.map((option, index) => (
                                                <a
                                                    key={index}
                                                    href="#"
                                                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-orange-500"
                                                    onClick={() => setIsSourceOpen(false)}
                                                >
                                                    {option}
                                                </a>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                            
                            {/* Sell on Alicartify Dropdown */}
                            <div className="relative">
                                <button 
                                    className="whitespace-nowrap text-gray-700 hover:text-orange-500 flex items-center"
                                    onClick={() => setIsSellOpen(!isSellOpen)}
                                >
                                    <ShopOutlined className="mr-1" />
                                    <span className="hidden xl:inline">Sell on Alicartify</span>
                                    <span className="xl:hidden">Sell</span>
                                    <DownOutlined className="ml-1 text-xs" />
                                </button>
                                
                                {isSellOpen && (
                                    <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                        <div className="py-2">
                                            {sellOptions.map((option, index) => (
                                                <a
                                                    key={index}
                                                    href="#"
                                                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-orange-500"
                                                    onClick={() => setIsSellOpen(false)}
                                                >
                                                    {option}
                                                </a>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                            
                            {/* Help Dropdown */}
                            <div className="relative">
                                <button 
                                    className="whitespace-nowrap text-gray-700 hover:text-orange-500 flex items-center"
                                    onClick={() => setIsHelpOpen(!isHelpOpen)}
                                >
                                    <CustomerServiceOutlined className="mr-1" />
                                    <span>Help</span>
                                    <DownOutlined className="ml-1 text-xs" />
                                </button>
                                
                                {isHelpOpen && (
                                    <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                        <div className="py-2">
                                            {helpOptions.map((option, index) => (
                                                <a
                                                    key={index}
                                                    href="#"
                                                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-orange-500"
                                                    onClick={() => setIsHelpOpen(false)}
                                                >
                                                    {option}
                                                </a>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Mobile Menu Button */}
                        <div className="lg:hidden">
                            <button 
                                onClick={() => {
                                    setIsMobileMenuOpen(!isMobileMenuOpen);
                                    setIsCurrencyOpen(false);
                                }}
                                className="text-gray-700 hover:text-orange-500 p-2 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                style={{ minHeight: '44px', minWidth: '44px' }}
                            >
                                <MenuOutlined />
                            </button>
                        </div>

                        {/* Desktop Right Side Links */}
                        <div className="hidden md:flex items-center space-x-3 xl:space-x-6 ml-auto lg:ml-0">
                            
                            {/* Currency Dropdown */}
                            <div className="relative">
                                <button 
                                    className="flex items-center text-gray-700 hover:text-orange-500"
                                    onClick={() => setIsCurrencyOpen(!isCurrencyOpen)}
                                >
                                    <GlobalOutlined className="mr-1" />
                                    <span className="hidden sm:inline">{getCurrencySymbol()} {currentCurrency}</span>
                                    <span className="sm:hidden">{currentCurrency}</span>
                                    <DownOutlined className="ml-1 text-xs" />
                                </button>
                                
                                {/* Currency Dropdown Menu */}
                                {isCurrencyOpen && (
                                    <div className="absolute top-full right-0 mt-1 w-40 sm:w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
                                        <div className="py-2">
                                            {supportedCurrencies.map((currency) => (
                                                <button
                                                    key={currency.code}
                                                    className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 hover:text-orange-500 ${
                                                        currentCurrency === currency.code ? 'bg-orange-50 text-orange-500' : 'text-gray-700'
                                                    }`}
                                                    onClick={() => {
                                                        changeCurrency(currency.code);
                                                        setIsCurrencyOpen(false);
                                                    }}
                                                    disabled={loading}
                                                >
                                                    <span className="mr-2">{currency.symbol}</span>
                                                    {currency.code} - {currency.name}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Mobile Currency Dropdown */}
                        <div className="md:hidden ml-auto mr-2">
                            <div className="relative">
                                <button 
                                    className="flex items-center text-gray-700 hover:text-orange-500 p-2 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                    onClick={() => {
                                        setIsCurrencyOpen(!isCurrencyOpen);
                                        setIsMobileMenuOpen(false);
                                    }}
                                    style={{ minHeight: '44px', minWidth: '44px' }}
                                >
                                    <GlobalOutlined className="mr-1" />
                                    <span>{currentCurrency}</span>
                                    <DownOutlined className="ml-1 text-xs" />
                                </button>
                                
                                {/* Currency Dropdown Menu */}
                                {isCurrencyOpen && (
                                    <div className="absolute top-full right-0 mt-1 w-40 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
                                        <div className="py-2">
                                            {supportedCurrencies.map((currency) => (
                                                <button
                                                    key={currency.code}
                                                    className={`block w-full text-left px-4 py-3 text-sm hover:bg-gray-100 hover:text-orange-500 active:bg-gray-200 transition-colors touch-manipulation ${
                                                        currentCurrency === currency.code ? 'bg-orange-50 text-orange-500' : 'text-gray-700'
                                                    }`}
                                                    onClick={() => {
                                                        changeCurrency(currency.code);
                                                        setIsCurrencyOpen(false);
                                                    }}
                                                    disabled={loading}
                                                    style={{ minHeight: '44px' }}
                                                >
                                                    <span className="mr-2">{currency.symbol}</span>
                                                    {currency.code}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Mobile Menu */}
                    {isMobileMenuOpen && (
                        <div className="lg:hidden border-t border-gray-200 bg-white">
                            <div className="py-3 space-y-2">
                                {/* Mobile Categories */}
                                <div className="relative">
                                    <button 
                                        className="flex items-center w-full px-3 py-3 text-gray-700 hover:text-orange-500 hover:bg-gray-50 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                        onClick={() => setIsCategoriesOpen(!isCategoriesOpen)}
                                        style={{ minHeight: '44px' }}
                                    >
                                        <AppstoreOutlined className="mr-2" />
                                        <span>Categories</span>
                                        <DownOutlined className="ml-auto text-xs" />
                                    </button>
                                    
                                    {/* Mobile Categories Dropdown */}
                                    {isCategoriesOpen && (
                                        <div className="mt-2 ml-4 space-y-1">
                                            {categories.map((category, index) => (
                                                <a
                                                    key={index}
                                                    href="#"
                                                    className="flex items-center px-3 py-3 text-sm text-gray-600 hover:bg-gray-50 hover:text-orange-500 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                                    onClick={() => {
                                                        setIsCategoriesOpen(false);
                                                        setIsMobileMenuOpen(false);
                                                    }}
                                                    style={{ minHeight: '44px' }}
                                                >
                                                    <span className="mr-3">{category.icon}</span>
                                                    {category.name}
                                                </a>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                {/* Mobile Navigation Links */}
                                <a 
                                    href="#" 
                                    className="flex items-center px-3 py-3 text-gray-700 hover:text-orange-500 hover:bg-gray-50 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                    style={{ minHeight: '44px' }}
                                >
                                    <SafetyOutlined className="mr-2" />
                                    Ready to Ship
                                </a>
                                <a 
                                    href="#" 
                                    className="flex items-center px-3 py-3 text-gray-700 hover:text-orange-500 hover:bg-gray-50 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                    style={{ minHeight: '44px' }}
                                >
                                    <GlobalOutlined className="mr-2" />
                                    Trade Shows
                                </a>
                                <a 
                                    href="#" 
                                    className="block px-3 py-3 text-gray-700 hover:text-orange-500 hover:bg-gray-50 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                    style={{ minHeight: '44px' }}
                                >
                                    Personal Protective Equipment
                                </a>

                                {/* Mobile Source Dropdown */}
                                <div className="relative">
                                    <button 
                                        className="flex items-center w-full px-3 py-3 text-gray-700 hover:text-orange-500 hover:bg-gray-50 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                        onClick={() => setIsSourceOpen(!isSourceOpen)}
                                        style={{ minHeight: '44px' }}
                                    >
                                        <span>Source on Alicartify</span>
                                        <DownOutlined className="ml-auto text-xs" />
                                    </button>
                                    
                                    {isSourceOpen && (
                                        <div className="mt-2 ml-4 space-y-1">
                                            {sourceOptions.map((option, index) => (
                                                <a
                                                    key={index}
                                                    href="#"
                                                    className="block px-3 py-3 text-sm text-gray-600 hover:bg-gray-50 hover:text-orange-500 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                                    onClick={() => {
                                                        setIsSourceOpen(false);
                                                        setIsMobileMenuOpen(false);
                                                    }}
                                                    style={{ minHeight: '44px' }}
                                                >
                                                    {option}
                                                </a>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                {/* Mobile Sell Dropdown */}
                                <div className="relative">
                                    <button 
                                        className="flex items-center w-full px-3 py-3 text-gray-700 hover:text-orange-500 hover:bg-gray-50 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                        onClick={() => setIsSellOpen(!isSellOpen)}
                                        style={{ minHeight: '44px' }}
                                    >
                                        <ShopOutlined className="mr-2" />
                                        <span>Sell on Alicartify</span>
                                        <DownOutlined className="ml-auto text-xs" />
                                    </button>
                                    
                                    {isSellOpen && (
                                        <div className="mt-2 ml-4 space-y-1">
                                            {sellOptions.map((option, index) => (
                                                <a
                                                    key={index}
                                                    href="#"
                                                    className="block px-3 py-3 text-sm text-gray-600 hover:bg-gray-50 hover:text-orange-500 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                                    onClick={() => {
                                                        setIsSellOpen(false);
                                                        setIsMobileMenuOpen(false);
                                                    }}
                                                    style={{ minHeight: '44px' }}
                                                >
                                                    {option}
                                                </a>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                {/* Mobile Help Dropdown */}
                                <div className="relative">
                                    <button 
                                        className="flex items-center w-full px-3 py-3 text-gray-700 hover:text-orange-500 hover:bg-gray-50 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                        onClick={() => setIsHelpOpen(!isHelpOpen)}
                                        style={{ minHeight: '44px' }}
                                    >
                                        <CustomerServiceOutlined className="mr-2" />
                                        <span>Help</span>
                                        <DownOutlined className="ml-auto text-xs" />
                                    </button>
                                    
                                    {isHelpOpen && (
                                        <div className="mt-2 ml-4 space-y-1">
                                            {helpOptions.map((option, index) => (
                                                <a
                                                    key={index}
                                                    href="#"
                                                    className="block px-3 py-3 text-sm text-gray-600 hover:bg-gray-50 hover:text-orange-500 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                                    onClick={() => {
                                                        setIsHelpOpen(false);
                                                        setIsMobileMenuOpen(false);
                                                    }}
                                                    style={{ minHeight: '44px' }}
                                                >
                                                    {option}
                                                </a>
                                            ))}
                                        </div>
                                    )}
                                </div>

                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Notification Bar */}
            <div className="bg-orange-50 border-b border-orange-200 w-full">
                <div className="container mx-auto px-2 sm:px-4">
                    <div className="flex items-center h-6 sm:h-8 text-xs text-gray-600">
                        <div className="flex items-center">
                            <InfoCircleOutlined className="mr-1 sm:mr-2 text-orange-500 flex-shrink-0" />
                            <span className="truncate">
                                <span className="hidden sm:inline">Welcome to Alicartify - Your trusted global marketplace for quality products</span>
                                <span className="sm:hidden">Welcome to Alicartify marketplace</span>
                            </span>
                            <a href="#" className="ml-1 sm:ml-2 text-orange-500 hover:underline whitespace-nowrap">learn more</a>
                            <span className="ml-1">›</span>
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Overlay to close dropdowns when clicking outside */}
            {(isCategoriesOpen || isCurrencyOpen || isSourceOpen || isSellOpen || isHelpOpen || isMobileMenuOpen) && (
                <div 
                    className="fixed inset-0 z-40" 
                    onClick={closeAllModals}
                ></div>
            )}
        </>
    );
}

export default Header;