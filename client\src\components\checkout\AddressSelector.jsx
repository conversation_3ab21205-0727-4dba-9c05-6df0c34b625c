import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Select, 
  Radio, 
  Typography, 
  notification,
  Space,
  Divider,
  Row,
  Col
} from 'antd';
import { 
  EditOutlined, 
  CheckCircleOutlined, 
  PlusOutlined, 
  HomeOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const AddressSelector = ({ selectedAddressId, onAddressSelect }) => {
  const { user, updateProfile } = useAuth();
  const [addresses, setAddresses] = useState([]);
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddressId, setEditingAddressId] = useState(null);
  const [newAddress, setNewAddress] = useState({
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'India',
    label: 'HOME' // Default label
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    // Load existing addresses from user profile
    if (user) {
      const existingAddresses = [];
      
      // Add main profile address if complete
      const { address: street, city, state, zipCode, country } = user;
      if (street && city && state && zipCode) {
        const profileAddress = {
          id: 'profile',
          address: street,
          city,
          state,
          zipCode,
          country: country || 'India',
          label: 'HOME',
          isDefault: true
        };
        existingAddresses.push(profileAddress);
      }
      
      // TODO: Add support for multiple saved addresses from user.addresses array
      // if (user.addresses && Array.isArray(user.addresses)) {
      //   user.addresses.forEach((addr, index) => {
      //     existingAddresses.push({ ...addr, id: `address_${index}` });
      //   });
      // }
      
      setAddresses(existingAddresses);
      
      // Auto-select the first available address
      if (existingAddresses.length > 0 && !selectedAddressId) {
        const defaultAddress = existingAddresses[0];
        setSelectedAddress(defaultAddress);
        onAddressSelect(defaultAddress.id);
      } else if (selectedAddressId) {
        const selected = existingAddresses.find(addr => addr.id === selectedAddressId);
        if (selected) {
          setSelectedAddress(selected);
        }
      }
      
      // If no addresses exist, show add form immediately
      if (existingAddresses.length === 0) {
        setShowAddForm(true);
      }
    }
  }, [user, selectedAddressId, onAddressSelect]);

  const handleAddressSelect = (address) => {
    setSelectedAddress(address);
    onAddressSelect(address.id);
    setShowAddForm(false);
    setEditingAddressId(null);
  };


  const handleSaveNewAddress = async () => {
    // Validate required fields
    const requiredFields = ['address', 'city', 'state', 'zipCode'];
    const missingFields = requiredFields.filter(field => !newAddress[field].trim());
    
    if (missingFields.length > 0) {
      notification.error({
        message: 'Incomplete Address',
        description: 'Please fill in all required fields',
      });
      return;
    }

    try {
      setSaving(true);
      
      if (editingAddressId) {
        // Update existing address (for now, we'll update the profile)
        const response = await updateProfile(newAddress);
        if (response.success) {
          notification.success({ message: 'Address updated successfully' });
          // Refresh addresses
          const updatedAddress = { ...newAddress, id: editingAddressId, isDefault: true };
          setAddresses(prev => prev.map(addr => 
            addr.id === editingAddressId ? updatedAddress : addr
          ));
          handleAddressSelect(updatedAddress);
        } else {
          throw new Error(response.error || 'Failed to update address');
        }
      } else {
        // Add new address (for now, we'll update the profile)
        const response = await updateProfile(newAddress);
        if (response.success) {
          notification.success({ message: 'Address saved successfully' });
          // Create new address object
          const savedAddress = { 
            ...newAddress, 
            id: `address_${Date.now()}`, 
            isDefault: addresses.length === 0 
          };
          setAddresses(prev => [...prev, savedAddress]);
          handleAddressSelect(savedAddress);
        } else {
          throw new Error(response.error || 'Failed to save address');
        }
      }
      
      // Reset form
      setNewAddress({
        address: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'India',
        label: 'HOME'
      });
      setShowAddForm(false);
      setEditingAddressId(null);
      
    } catch (error) {
      notification.error({
        message: 'Failed to save address',
        description: error.message || error,
      });
    } finally {
      setSaving(false);
    }
  };

  const handleEditAddress = (address) => {
    setNewAddress({ ...address });
    setEditingAddressId(address.id);
    setShowAddForm(true);
  };

  const handleAddNewAddress = () => {
    setNewAddress({
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'India',
      label: 'HOME'
    });
    setEditingAddressId(null);
    setShowAddForm(true);
  };

  const renderAddressCard = (address) => {
    const isSelected = selectedAddress?.id === address.id;
    
    return (
      <div 
        key={address.id}
        className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
          isSelected 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-200 hover:border-gray-300'
        }`}
        onClick={() => handleAddressSelect(address)}
      >
        <div className="flex items-start justify-between">
          <Radio 
            checked={isSelected}
            onChange={() => handleAddressSelect(address)}
            className="mt-1"
          >
            <div className="ml-2">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-xs font-medium px-2 py-1 bg-gray-100 rounded text-gray-700">
                  {address.label}
                </span>
                {address.isDefault && (
                  <span className="text-xs font-medium px-2 py-1 bg-green-100 rounded text-green-700">
                    DEFAULT
                  </span>
                )}
              </div>
              
              <div className="font-medium text-gray-900 mb-1">
                {user?.firstName} {user?.lastName}
              </div>
              
              <div className="text-gray-700 mb-1">
                {address.address}
              </div>
              
              <div className="text-gray-600 text-sm">
                {address.city}, {address.state} - {address.zipCode}
              </div>
              
              <div className="text-gray-500 text-sm">
                {address.country}
              </div>
              
              {isSelected && (
                <div className="mt-3">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <CheckCircleOutlined className="mr-1" />
                    DELIVER HERE
                  </span>
                </div>
              )}
            </div>
          </Radio>
          
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleEditAddress(address);
            }}
            className="text-blue-600"
          >
            Edit
          </Button>
        </div>
      </div>
    );
  };

  const hasCompleteAddress = address.address && address.city && address.state && address.zipCode;

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <Space align="center">
          <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
            2
          </div>
          <Title level={4} className="mb-0">DELIVERY ADDRESS</Title>
        </Space>
        {hasCompleteAddress && !isEditing && (
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => setIsEditing(true)}
          >
            CHANGE
          </Button>
        )}
      </div>
      
      <Divider className="my-4" />
      
      {!isEditing && hasCompleteAddress ? (
        // Display saved address
        <div className="border-2 border-blue-300 bg-blue-50 rounded-lg p-4">
          <Radio.Group value="selected" className="w-full">
            <Radio value="selected" className="w-full">
              <div className="ml-2">
                <div className="font-medium text-gray-900 mb-1">
                  {address.address}
                </div>
                <Text type="secondary" className="block">
                  {address.city}, {address.state} - {address.zipCode}
                </Text>
                <Text type="secondary" className="block">
                  {address.country}
                </Text>
                <div className="mt-3">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <CheckCircleOutlined className="mr-1" />
                    DELIVER HERE
                  </span>
                </div>
              </div>
            </Radio>
          </Radio.Group>
        </div>
      ) : (
        // Address form using Ant Design
        <div>
          <Text type="secondary" className="block mb-4">
            {hasCompleteAddress ? 'Update your delivery address:' : 'Please add your delivery address:'}
          </Text>
          
          <Form
            layout="vertical"
            onFinish={handleSaveAddress}
            initialValues={address}
          >
            <Form.Item
              label="Address"
              name="address"
              rules={[{ required: true, message: 'Please enter your address!' }]}
            >
              <TextArea
                value={address.address}
                onChange={(e) => setAddress(prev => ({ ...prev, address: e.target.value }))}
                placeholder="House No, Building Name, Road Name, Area"
                rows={3}
              />
            </Form.Item>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                label="City"
                name="city"
                rules={[{ required: true, message: 'Please enter your city!' }]}
              >
                <Input
                  value={address.city}
                  onChange={(e) => setAddress(prev => ({ ...prev, city: e.target.value }))}
                  placeholder="City"
                />
              </Form.Item>
              
              <Form.Item
                label="State"
                name="state"
                rules={[{ required: true, message: 'Please enter your state!' }]}
              >
                <Input
                  value={address.state}
                  onChange={(e) => setAddress(prev => ({ ...prev, state: e.target.value }))}
                  placeholder="State"
                />
              </Form.Item>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                label="PIN Code"
                name="zipCode"
                rules={[
                  { required: true, message: 'Please enter your PIN code!' },
                  { pattern: /^[0-9]{6}$/, message: 'Please enter a valid 6-digit PIN code!' }
                ]}
              >
                <Input
                  value={address.zipCode}
                  onChange={(e) => setAddress(prev => ({ ...prev, zipCode: e.target.value }))}
                  placeholder="PIN Code"
                  maxLength={6}
                />
              </Form.Item>
              
              <Form.Item
                label="Country"
                name="country"
              >
                <Select
                  value={address.country}
                  onChange={(value) => setAddress(prev => ({ ...prev, country: value }))}
                >
                  <Option value="India">India</Option>
                  <Option value="United States">United States</Option>
                  <Option value="United Kingdom">United Kingdom</Option>
                  <Option value="Canada">Canada</Option>
                  <Option value="Australia">Australia</Option>
                </Select>
              </Form.Item>
            </div>
            
            <Form.Item className="mb-0">
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={saving}
                  size="large"
                  style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35' }}
                >
                  SAVE AND DELIVER HERE
                </Button>
                
                {hasCompleteAddress && (
                  <Button
                    size="large"
                    onClick={() => setIsEditing(false)}
                  >
                    CANCEL
                  </Button>
                )}
              </Space>
            </Form.Item>
          </Form>
        </div>
      )}
    </Card>
  );
};

export default AddressSelector;
