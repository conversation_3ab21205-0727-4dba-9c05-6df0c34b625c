import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://multi-vendor-server-1tb9.onrender.com';

// Create axios instance with default config
const adminApi = axios.create({
  baseURL: API_BASE_URL.endsWith('/api') ? `${API_BASE_URL}/admin` : `${API_BASE_URL}/api/admin`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
adminApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle response errors
adminApi.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('token');
      localStorage.removeItem('authUser');
      localStorage.removeItem('authUserType');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Dashboard APIs
export const dashboardApi = {
  getStats: () => adminApi.get('/dashboard/stats'),
  getRealTimeMetrics: () => adminApi.get('/dashboard/realtime'),
  getAnalytics: (params) => adminApi.get('/dashboard/analytics', { params }),
  getSystemHealth: () => adminApi.get('/dashboard/health'),
};

// Users APIs
export const usersApi = {
  getUsers: (params) => adminApi.get('/users', { params }),
  getUserById: (id) => adminApi.get(`/users/${id}`),
  createUser: (data) => adminApi.post('/users', data),
  updateUser: (id, data) => adminApi.put(`/users/${id}`, data),
  updateUserStatus: (id, status) => adminApi.patch(`/users/${id}/status`, { status }),
  resetUserPassword: (id, newPassword) => adminApi.patch(`/users/${id}/reset-password`, { newPassword }),
  deleteUser: (id) => adminApi.delete(`/users/${id}`),
  getUserStatistics: () => adminApi.get('/users/statistics'),
  bulkUpdateUsers: (userIds, updates) => adminApi.patch('/users/bulk-update', { userIds, updates }),
};

// Vendors APIs
export const vendorsApi = {
  getVendors: (params) => adminApi.get('/vendors', { params }),
  getVendorById: (id) => adminApi.get(`/vendors/${id}`),
  updateVendor: (id, data) => adminApi.put(`/vendors/${id}`, data),
  updateVendorStatus: (id, status) => adminApi.patch(`/vendors/${id}/status`, { status }),
  updateVerificationStatus: (id, status, rejectionReason) =>
    adminApi.patch(`/vendors/${id}/verification`, { status, rejectionReason }),
  updateCommission: (id, data) => adminApi.patch(`/vendors/${id}/commission`, data),
  deleteVendor: (id) => adminApi.delete(`/vendors/${id}`),
  getStatistics: () => adminApi.get('/vendors/statistics'),
  bulkUpdateVendors: (vendorIds, updates) => adminApi.patch('/vendors/bulk-update', { vendorIds, updates }),

  // New vendor management APIs
  getPendingVerification: (params) => adminApi.get('/vendors/pending-verification', { params }),
  verifyVendor: (id, data) => adminApi.patch(`/vendors/${id}/verify`, data),
  rejectVerification: (id, data) => adminApi.patch(`/vendors/${id}/reject-verification`, data),
  approveVendor: (id) => adminApi.patch(`/vendors/${id}/approve`),
  suspendVendor: (id) => adminApi.patch(`/vendors/${id}/suspend`),
  processPayout: (id, data) => adminApi.post(`/vendors/${id}/payout`, data),
  getCommissionReport: (params) => adminApi.get('/vendors/commission-report', { params }),
};

// Orders APIs
export const ordersApi = {
  getOrders: (params) => adminApi.get('/orders', { params }),
  getOrderById: (id) => adminApi.get(`/orders/${id}`),
  updateOrderStatus: (id, status, note) => adminApi.patch(`/orders/${id}/status`, { status, note }),
  updatePaymentStatus: (id, paymentStatus, transactionId, refundAmount) => 
    adminApi.patch(`/orders/${id}/payment`, { paymentStatus, transactionId, refundAmount }),
  updateShippingInfo: (id, trackingNumber, carrier, estimatedDelivery) => 
    adminApi.patch(`/orders/${id}/shipping`, { trackingNumber, carrier, estimatedDelivery }),
  addOrderNote: (id, message, isPrivate) => adminApi.post(`/orders/${id}/notes`, { message, isPrivate }),
  getOrderStatistics: () => adminApi.get('/orders/statistics'),
  bulkUpdateOrders: (orderIds, updates) => adminApi.patch('/orders/bulk-update', { orderIds, updates }),
  exportOrders: (params) => adminApi.get('/orders/export', { params }),
};

// Products APIs
export const productsApi = {
  getAll: (params) => adminApi.get('/products', { params }),
  getById: (id) => adminApi.get(`/products/${id}`),
  updateStatus: (id, data) => adminApi.patch(`/products/${id}/status`, data),
  toggleFeatured: (id, data) => adminApi.patch(`/products/${id}/featured`, data),
  bulkOperation: (data) => adminApi.post('/products/bulk', data),
  getStats: () => adminApi.get('/products/stats'),
  getLowStock: () => adminApi.get('/products/low-stock'),
  getByVendor: (vendorId, params) => adminApi.get(`/products/vendor/${vendorId}`, { params }),

  // Approval APIs
  getPendingApproval: (params) => adminApi.get('/products/pending-approval', { params }),
  approve: (id, data) => adminApi.patch(`/products/${id}/approve`, data),
  reject: (id, data) => adminApi.patch(`/products/${id}/reject`, data),
  requestChanges: (id, data) => adminApi.patch(`/products/${id}/request-changes`, data),
};

// Categories APIs (placeholder - you can implement these later)
export const categoriesApi = {
  getCategories: (params) => adminApi.get('/categories', { params }),
  getCategoryById: (id) => adminApi.get(`/categories/${id}`),
  createCategory: (data) => adminApi.post('/categories', data),
  updateCategory: (id, data) => adminApi.put(`/categories/${id}`, data),
  deleteCategory: (id) => adminApi.delete(`/categories/${id}`),
  getCategoryStatistics: () => adminApi.get('/categories/statistics'),
  getAllCategories: () => adminApi.get('/categories?limit=1000'), // Get all categories for selection
};

// Analytics APIs (placeholder - you can implement these later)
export const analyticsApi = {
  getAnalytics: (params) => adminApi.get('/analytics', { params }),
  getReports: (params) => adminApi.get('/analytics/reports', { params }),
};

// Settings APIs
export const settingsApi = {
  getSettings: () => adminApi.get('/settings'),
  getSettingsByCategory: (category) => adminApi.get(`/settings/category/${category}`),
  updateSettings: (data) => adminApi.put('/settings', data),
  updateSetting: (key, data) => adminApi.put(`/settings/${key}`, data),
  deleteSetting: (key) => adminApi.delete(`/settings/${key}`),
  initializeDefaultSettings: () => adminApi.post('/settings/initialize'),
};

// Homepage Settings APIs
export const homepageSettingsApi = {
  getSettings: () => adminApi.get('/homepage-settings'),
  updateGeneralSettings: (data) => adminApi.put('/homepage-settings/settings', data),

  // Carousel APIs
  addCarouselImage: (data) => adminApi.post('/homepage-settings/carousel', data),
  updateCarouselImage: (imageId, data) => adminApi.put(`/homepage-settings/carousel/${imageId}`, data),
  deleteCarouselImage: (imageId) => adminApi.delete(`/homepage-settings/carousel/${imageId}`),

  // Promotion APIs
  addPromotionImage: (data) => adminApi.post('/homepage-settings/promotions', data),
  updatePromotionImage: (imageId, data) => adminApi.put(`/homepage-settings/promotions/${imageId}`, data),
  deletePromotionImage: (imageId) => adminApi.delete(`/homepage-settings/promotions/${imageId}`),

  // Featured Category APIs
  addFeaturedCategory: (formData) => adminApi.post('/homepage-settings/featured-categories', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  updateFeaturedCategory: (categoryId, formData) => adminApi.put(`/homepage-settings/featured-categories/${categoryId}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteFeaturedCategory: (categoryId) => adminApi.delete(`/homepage-settings/featured-categories/${categoryId}`)
};

// Notifications APIs
export const notificationsApi = {
  getNotifications: (params) => adminApi.get('/notifications', { params }),
  getStats: () => adminApi.get('/notifications/stats'),
  sendAnnouncement: (data) => adminApi.post('/notifications/announcement', data),
  markAsRead: (id) => adminApi.patch(`/notifications/${id}/read`),
  archive: (id) => adminApi.patch(`/notifications/${id}/archive`),
  delete: (id) => adminApi.delete(`/notifications/${id}`),
  bulkOperation: (data) => adminApi.post('/notifications/bulk', data),
};

export default adminApi;