import React from 'react';
import CategoryCard from './ui/CategoryCard';

const ItemsCard = () => {
    // Sample data for different categories
    const categories = [
        {
            id: 'new-arrivals',
            title: 'New Arrivals',
            icon: (
                <svg
                    className="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                    />
                </svg>
            ),
            items: [
                {
                    id: 1,
                    image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=300&auto=format&fit=crop",
                    price: "$0.01",
                    badgeText: "Trending Now",
                    alt: "Fashion item 1"
                },
                {
                    id: 2,
                    image: "https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=300&h=300&auto=format&fit=crop",
                    price: "$1.00",
                    badgeText: "Trending Now",
                    alt: "Fashion item 2"
                },
                {
                    id: 3,
                    image: "https://images.unsplash.com/photo-1503342217505-b0a15ec3261c?w=300&h=300&auto=format&fit=crop",
                    price: "$1.31",
                    badgeText: "Trending Now",
                    alt: "Fashion item 3"
                }
            ]
        },
        {
            id: 'top-ranked',
            title: 'Top-ranked Products',
            icon: (
                <svg
                    className="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                </svg>
            ),
            items: [
                {
                    id: 4,
                    image: "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=300&h=300&auto=format&fit=crop",
                    price: "$0.80",
                    badgeText: "2 Pieces",
                    alt: "Product 1"
                },
                {
                    id: 5,
                    image: "https://images.unsplash.com/photo-1485230895905-ec40ba36b9bc?w=300&h=300&auto=format&fit=crop",
                    price: "$4.30",
                    badgeText: "12 Pieces",
                    alt: "Product 2"
                },
                {
                    id: 6,
                    image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300&h=300&auto=format&fit=crop",
                    price: "$4.89",
                    badgeText: "2 Pairs",
                    alt: "Product 3"
                }
            ]
        },
        {
            id: 'ppe',
            title: 'Personal Protective Equipment',
            icon: (
                <svg
                    className="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    />
                </svg>
            ),
            items: [
                {
                    id: 7,
                    image: "https://images.unsplash.com/photo-1584634731339-252c581abfc5?w=300&h=300&auto=format&fit=crop",
                    price: "$0.25",
                    alt: "Mask"
                },
                {
                    id: 8,
                    image: "https://images.unsplash.com/photo-1584365685547-9a5fb6f3a70c?w=300&h=300&auto=format&fit=crop",
                    price: "$0.70",
                    alt: "Sanitizer"
                },
                {
                    id: 9,
                    image: "https://images.unsplash.com/photo-1583947581924-860bda6a26df?w=300&h=300&auto=format&fit=crop",
                    price: "$0.13",
                    alt: "Gloves"
                }
            ]
        },
        
    ];

    return (
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categories.map(category => (
                    <CategoryCard
                        key={category.id}
                        title={category.title}
                        icon={category.icon}
                        items={category.items}
                    />
                ))}
            </div>
        </div>
    );
};

export default ItemsCard;