import React, { useState, useEffect } from 'react';
import { Carousel, Spin, Alert } from 'antd';
import axios from 'axios';
import { getApiUrl } from '../../config/api';

const AutoScrollCarousel = () => {
  const [carouselData, setCarouselData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchHomepageData();
  }, []);

  const fetchHomepageData = async () => {
    try {
      setLoading(true);
      const response = await axios.get(getApiUrl('public/homepage/data'));
      if (response.data.success) {
        setCarouselData(response.data.data.carousel);
      }
    } catch (error) {
      console.error('Error fetching carousel data:', error);
      setError('Failed to load carousel images');
    } finally {
      setLoading(false);
    }
  };

  const handleImageClick = (linkUrl) => {
    if (linkUrl) {
      window.open(linkUrl, '_blank');
    }
  };

  if (loading) {
    return (
      <div className='w-full max-w-full sm:max-w-xl md:max-w-2xl lg:max-w-4xl xl:max-w-6xl h-fit mx-auto flex items-center justify-center'>
        <Spin size="large" />
      </div>
    );
  }

  if (error || !carouselData?.images?.length) {
    return (
      <div className='w-full max-w-full sm:max-w-xl md:max-w-2xl lg:max-w-4xl xl:max-w-6xl h-fit mx-auto'>
        <Alert
          message="No carousel images available"
          description="Please contact administrator to add carousel images."
          type="info"
          showIcon
        />
      </div>
    );
  }

  return (
    <div className='w-full max-w-full sm:max-w-xl md:max-w-2xl lg:max-w-4xl xl:max-w-6xl h-fit mx-auto'>
      <Carousel
        autoplay={carouselData.settings?.autoPlay !== false}
        autoplaySpeed={carouselData.settings?.speed || 3000}
        className='w-full h-full'
      >
        {carouselData.images.map((image, index) => (
          <div key={image._id || index}>
            <img
              src={image.imageUrl}
              alt={image.title || `Carousel ${index + 1}`}
              className="h-44 sm:h-52 md:h-60 lg:h-64 xl:h-72 w-full object-cover rounded cursor-pointer"
              onClick={() => handleImageClick(image.linkUrl)}
              onError={(e) => {
                e.target.src = 'https://via.placeholder.com/1620x270?text=Image+Not+Found';
              }}
            />
            {image.title && (
              <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white p-2 rounded">
                <h3 className="text-lg font-semibold">{image.title}</h3>
                {image.description && (
                  <p className="text-sm">{image.description}</p>
                )}
              </div>
            )}
          </div>
        ))}
      </Carousel>
    </div>
  );
};

export default AutoScrollCarousel;