const express = require('express');
const router = express.Router();

// Import middleware
const { optionalAuth } = require('../../middleware/auth/authMiddleware');
const { addUserCurrency } = require('../../middleware/currency');

// Import validation middleware
const { validateProductList, validateProductId } = require('../../middleware/validation/productValidation');

// Import controller
const {
  getProducts,
  getProduct,
  getFeaturedProducts,
  getNewArrivals,
  getBestSelling,
  getProductsByCategory,
  getProductsByVendor,
  searchProducts
} = require('../../controllers/public/productController');

/**
 * @route   GET /api/public/products/featured
 * @desc    Get featured products
 * @access  Public
 */
router.get('/featured', optionalAuth, addUserCurrency, getFeaturedProducts);

/**
 * @route   GET /api/public/products/new-arrivals
 * @desc    Get new arrival products
 * @access  Public
 */
router.get('/new-arrivals', optionalAuth, addUserCurrency, getNewArrivals);

/**
 * @route   GET /api/public/products/best-selling
 * @desc    Get best selling products
 * @access  Public
 */
router.get('/best-selling', optionalAuth, addUserCurrency, getBestSelling);

/**
 * @route   GET /api/public/products/search
 * @desc    Search products
 * @access  Public
 */
router.get('/search', optionalAuth, addUserCurrency, searchProducts);

/**
 * @route   GET /api/public/products/category/:categoryId
 * @desc    Get products by category
 * @access  Public
 */
router.get('/category/:categoryId', optionalAuth, addUserCurrency, getProductsByCategory);

/**
 * @route   GET /api/public/products/vendor/:vendorId
 * @desc    Get products by vendor
 * @access  Public
 */
router.get('/vendor/:vendorId', optionalAuth, addUserCurrency, getProductsByVendor);

/**
 * @route   GET /api/public/products
 * @desc    Get all active products with pagination and filters
 * @access  Public
 */
router.get('/', optionalAuth, addUserCurrency, validateProductList, getProducts);

/**
 * @route   GET /api/public/products/:id
 * @desc    Get single product by ID or slug
 * @access  Public
 */
router.get('/:id', optionalAuth, addUserCurrency, getProduct);

module.exports = router;