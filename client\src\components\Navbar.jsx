import React, { useState } from "react";
import { MessageOutlined, MenuOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";
import SearchContainer from "./SearchContainer";
import UserMenu from "./UserMenu";
import CartMenu from "./CartMenu";
import MobileSearchBar from "./MobileSearchBar";
import MobileMenu from "./MobileMenu";

const Navbar = () => {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const navigate = useNavigate();
    const { isAuthenticated, userType } = useAuth();

    const closeAllModals = () => {
        setIsMobileMenuOpen(false);
    };

    const handleOrdersClick = () => {
        navigate('/orders');
        setIsMobileMenuOpen(false);
    };

    return (
        <>
            <div className="border-b border-gray-800 bg-black shadow-sm">
                <div className="container mx-auto px-2 sm:px-4">
                    {/* Main Navbar */}
                    <div className="flex items-center justify-between h-14 sm:h-16">
                        {/* Logo */}
                        <div className="flex items-center flex-shrink-0">
                            <a href="/" className="flex items-center">
                                <img 
                                    src="https://i.ibb.co/9mQR1Z1H/Alicartify-Logo.png" 
                                    alt="Alicartify Logo"
                                    className="h-8 sm:h-10 w-auto object-contain"
                                />
                            </a>
                        </div>

                        {/* Desktop Search Bar */}
                        <SearchContainer closeAllModals={closeAllModals} />

                        {/* Desktop Right Navigation */}
                        <div className="hidden lg:flex items-center space-x-3 xl:space-x-6">
                            {/* Join Free - only show if not authenticated */}
                            {!isAuthenticated && (
                                <button 
                                    onClick={() => navigate('/auth')}
                                    className="hidden xl:block text-sm text-white hover:text-orange-500 whitespace-nowrap"
                                >
                                    Join Free
                                </button>
                            )}

                            {/* Messages */}
                            <a href="#" className="hidden xl:flex items-center text-sm text-white hover:text-orange-500 whitespace-nowrap">
                                <MessageOutlined className="mr-1" />
                                <span>Messages</span>
                            </a>

                            {/* Orders */}
                            <button 
                                onClick={handleOrdersClick}
                                className="text-sm text-white hover:text-orange-500 whitespace-nowrap"
                            >
                                <span>Orders</span>
                            </button>

                            {/* Test Panels - Development only */}
                            <button 
                                onClick={() => navigate('/test-panels')}
                                className="text-sm text-yellow-400 hover:text-yellow-300 whitespace-nowrap"
                            >
                                <span>Test Panels</span>
                            </button>

                            {/* User Icons */}
                            <div className="flex items-center space-x-3 xl:space-x-4">
                                <UserMenu onMenuClose={closeAllModals} />
                                {userType === 'customer' && (
                                    <CartMenu onMenuClose={closeAllModals} />
                                )}
                            </div>
                        </div>

                        {/* Mobile Menu Button and Icons */}
                        <div className="lg:hidden flex items-center space-x-2">
                            <div className="flex items-center space-x-2">
                                <UserMenu isMobile={true} onMenuClose={closeAllModals} />
                                {userType === 'customer' && (
                                    <CartMenu isMobile={true} onMenuClose={closeAllModals} />
                                )}
                            </div>

                            {/* Mobile Menu Button */}
                            <button 
                                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                                className="text-white hover:text-orange-500 text-lg p-2 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
                                style={{ minHeight: '44px', minWidth: '44px' }}
                            >
                                <MenuOutlined />
                            </button>
                        </div>
                    </div>

                    {/* Mobile Search Bar */}
                    <MobileSearchBar closeAllModals={closeAllModals} />

                    {/* Mobile Menu */}
                    <MobileMenu 
                        isMobileMenuOpen={isMobileMenuOpen} 
                        onMenuClose={() => setIsMobileMenuOpen(false)} 
                    />
                </div>
            </div>
        </>
    );
};

export default Navbar;