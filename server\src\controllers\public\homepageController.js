const HomepageSettings = require('../../models/HomepageSettings');
const Category = require('../../models/Category');

// Get public homepage settings
const getHomepageData = async (req, res) => {
  try {
    let settings = await HomepageSettings.getSettings();
    settings = await HomepageSettings.populate(settings, [
      {
        path: 'featuredCategories.category',
        select: 'name slug image'
      },
      {
        path: 'featuredCategoryIds',
        select: 'name slug image'
      }
    ]);
    
    // Filter only active and current items
    const now = new Date();
    
    const activeCarouselImages = settings.carouselImages
      .filter(img => img.isActive)
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .map(img => ({
        _id: img._id,
        title: img.title,
        description: img.description,
        imageUrl: img.imageUrl,
        linkUrl: img.linkUrl
      }));
    
    const activeFeaturedCategories = settings.featuredCategories
      .filter(cat => cat.isActive && cat.category)
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .map(cat => ({
        _id: cat._id,
        displayName: cat.displayName || cat.category.name,
        imageUrl: cat.imageUrl,
        category: {
          _id: cat.category._id,
          name: cat.category.name,
          slug: cat.category.slug
        }
      }));
    
    const activePromotions = settings.promotionImages
      .filter(promo => {
        if (!promo.isActive) return false;
        if (promo.startDate && promo.startDate > now) return false;
        if (promo.endDate && promo.endDate < now) return false;
        return true;
      })
      .reduce((acc, promo) => {
        if (!acc[promo.position]) acc[promo.position] = [];
        acc[promo.position].push({
          _id: promo._id,
          title: promo.title,
          description: promo.description,
          imageUrl: promo.imageUrl,
          linkUrl: promo.linkUrl,
          position: promo.position
        });
        return acc;
      }, {});
    
    // Sort promotions by sortOrder within each position
    Object.keys(activePromotions).forEach(position => {
      activePromotions[position].sort((a, b) => a.sortOrder - b.sortOrder);
    });
    
    const responseData = {
      carousel: {
        images: activeCarouselImages,
        settings: {
          autoPlay: settings.settings.autoPlayCarousel,
          speed: settings.settings.carouselSpeed
        }
      },
      featuredCategories: activeFeaturedCategories,
      promotions: activePromotions,
      showPromotions: settings.settings.showPromotions
    };
    
    res.status(200).json({
      success: true,
      data: responseData
    });
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch homepage data',
      error: error.message
    });
  }
};

// Get featured categories for navigation
const getFeaturedCategories = async (req, res) => {
  try {
    const settings = await HomepageSettings.getSettings()
      .populate('featuredCategories.category', 'name slug image');
    
    const categories = settings.featuredCategories
      .filter(cat => cat.isActive && cat.category)
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .map(cat => ({
        _id: cat.category._id,
        name: cat.displayName || cat.category.name,
        slug: cat.category.slug,
        imageUrl: cat.imageUrl || cat.category.image
      }));
    
    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching featured categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch featured categories',
      error: error.message
    });
  }
};

module.exports = {
  getHomepageData,
  getFeaturedCategories
};
