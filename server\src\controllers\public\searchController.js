const Product = require('../../models/Product');
const Category = require('../../models/Category');
const Vendor = require('../../models/Vendor');

/**
 * Search products
 */
const searchProducts = async (req, res) => {
  try {
    const { q, category, vendor, minPrice, maxPrice, page = 1, limit = 20, sortBy = 'relevance', inStock } = req.query;

    // Validate query parameter
    if (!q || typeof q !== 'string' || q.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters long and valid'
      });
    }

    // Sanitize search term
    const searchTerm = q.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // Escape regex special chars

    const searchQuery = {
      status: 'active',
      $or: [
        { name: { $regex: searchTerm, $options: 'i' } },
        { description: { $regex: searchTerm, $options: 'i' } },
        { tags: { $in: [new RegExp(searchTerm, 'i')] } }
      ]
    };

    // Add stock filter if specified
    if (inStock === 'true') {
      searchQuery['inventory.quantity'] = { $gt: 0 };
    }

    // Add category filter
    if (category) {
      searchQuery.category = category;
    }

    // Add vendor filter
    if (vendor) {
      searchQuery.vendor = vendor;
    }

    // Handle price filters separately to avoid interfering with text search
    const textSearchOr = searchQuery.$or; // Save the text search $or
    
    // Validate and sanitize numeric filters - handle both basePrice and salePrice
    if (minPrice && !isNaN(parseFloat(minPrice)) && maxPrice && !isNaN(parseFloat(maxPrice))) {
      // Both min and max price filters
      searchQuery.$and = searchQuery.$and || [];
      searchQuery.$and.push({
        $or: [
          { $and: [
            { 'pricing.basePrice': { $gte: parseFloat(minPrice) } },
            { 'pricing.basePrice': { $lte: parseFloat(maxPrice) } }
          ]},
          { $and: [
            { 'pricing.salePrice': { $gte: parseFloat(minPrice) } },
            { 'pricing.salePrice': { $lte: parseFloat(maxPrice) } }
          ]}
        ]
      });
    } else if (minPrice && !isNaN(parseFloat(minPrice))) {
      // Only min price filter
      searchQuery.$and = searchQuery.$and || [];
      searchQuery.$and.push({
        $or: [
          { 'pricing.basePrice': { $gte: parseFloat(minPrice) } },
          { 'pricing.salePrice': { $gte: parseFloat(minPrice) } }
        ]
      });
    } else if (maxPrice && !isNaN(parseFloat(maxPrice))) {
      // Only max price filter
      searchQuery.$and = searchQuery.$and || [];
      searchQuery.$and.push({
        $or: [
          { 'pricing.basePrice': { $lte: parseFloat(maxPrice) } },
          { 'pricing.salePrice': { $lte: parseFloat(maxPrice) } }
        ]
      });
    }
    
    // Ensure text search $or is preserved
    searchQuery.$or = textSearchOr;

    // Validate pagination parameters
    const currentPage = Math.max(1, parseInt(page) || 1);
    const pageLimit = Math.min(100, Math.max(1, parseInt(limit) || 20)); // Cap at 100 items per page
    const skip = (currentPage - 1) * pageLimit;

    // Set up sorting
    let sortOptions = { createdAt: -1 }; // Default sort
    if (sortBy === 'price_asc') {
      sortOptions = { 'pricing.basePrice': 1 };
    } else if (sortBy === 'price_desc') {
      sortOptions = { 'pricing.basePrice': -1 };
    } else if (sortBy === 'rating') {
      sortOptions = { rating: -1 };
    } else if (sortBy === 'name') {
      sortOptions = { name: 1 };
    }

    const [products, total] = await Promise.all([
      Product.find(searchQuery)
        .populate('vendor', 'businessName slug')
        .populate('category', 'name slug')
        .select('name slug description pricing images rating reviewCount reviews vendor category inventory')
        .sort(sortOptions)
        .skip(skip)
        .limit(pageLimit),
      Product.countDocuments(searchQuery)
    ]);

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage,
          totalPages: Math.ceil(total / pageLimit),
          totalItems: total,
          itemsPerPage: pageLimit
        },
        searchQuery: q,
        filters: {
          category,
          vendor,
          minPrice,
          maxPrice,
          inStock,
          sortBy
        }
      }
    });

  } catch (error) {
    console.error('Search products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Search categories
 */
const searchCategories = async (req, res) => {
  try {
  const { q, page = 1, limit = 20 } = req.query;

  if (!q || q.trim().length < 2) {
    return res.status(400).json({
      success: false,
      message: 'Search query must be at least 2 characters long'
    });
  }

  const searchQuery = {
    status: 'active',
    $or: [
      { name: { $regex: q, $options: 'i' } },
      { description: { $regex: q, $options: 'i' } }
    ]
  };

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const [categories, total] = await Promise.all([
    Category.find(searchQuery)
      .select('name slug description image')
      .sort({ name: 1 })
      .skip(skip)
      .limit(parseInt(limit)),
    Category.countDocuments(searchQuery)
  ]);

  res.json({
    success: true,
    data: {
      categories,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / parseInt(limit)),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    }
  });

  } catch (error) {
    console.error('Search categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search categories',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Search vendors
 */
const searchVendors = async (req, res) => {
  try {
  const { q, page = 1, limit = 20 } = req.query;

  if (!q || q.trim().length < 2) {
    return res.status(400).json({
      success: false,
      message: 'Search query must be at least 2 characters long'
    });
  }

  const searchQuery = {
    status: 'active',
    $or: [
      { businessName: { $regex: q, $options: 'i' } },
      { description: { $regex: q, $options: 'i' } }
    ]
  };

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const [vendors, total] = await Promise.all([
    Vendor.find(searchQuery)
      .populate('user', 'firstName lastName')
      .select('businessName slug description logo rating reviewCount')
      .sort({ businessName: 1 })
      .skip(skip)
      .limit(parseInt(limit)),
    Vendor.countDocuments(searchQuery)
  ]);

  res.json({
    success: true,
    data: {
      vendors,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / parseInt(limit)),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    }
  });

  } catch (error) {
    console.error('Search vendors error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search vendors',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Global search across products, categories, and vendors
 */
const globalSearch = async (req, res) => {
  try {
  const { q, type = 'all', page = 1, limit = 20 } = req.query;

  if (!q || q.trim().length < 2) {
    return res.status(400).json({
      success: false,
      message: 'Search query must be at least 2 characters long'
    });
  }

  const results = {};

  if (type === 'all' || type === 'products') {
    const productQuery = {
      status: 'active',
      $or: [
        { name: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } },
        { tags: { $in: [new RegExp(q, 'i')] } }
      ]
    };

    results.products = await Product.find(productQuery)
      .populate('vendor', 'businessName slug')
      .populate('category', 'name slug')
      .select('name slug description price images rating reviewCount vendor category')
      .sort({ createdAt: -1 })
      .limit(type === 'products' ? parseInt(limit) : 5);
  }

  if (type === 'all' || type === 'categories') {
    const categoryQuery = {
      status: 'active',
      $or: [
        { name: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } }
      ]
    };

    results.categories = await Category.find(categoryQuery)
      .select('name slug description image')
      .sort({ name: 1 })
      .limit(type === 'categories' ? parseInt(limit) : 5);
  }

  if (type === 'all' || type === 'vendors') {
    const vendorQuery = {
      status: 'active',
      $or: [
        { businessName: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } }
      ]
    };

    results.vendors = await Vendor.find(vendorQuery)
      .populate('user', 'firstName lastName')
      .select('businessName slug description logo rating reviewCount')
      .sort({ businessName: 1 })
      .limit(type === 'vendors' ? parseInt(limit) : 5);
  }

  res.json({
    success: true,
    data: results
  });

  } catch (error) {
    console.error('Global search error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform global search',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get search suggestions
 */
const getSuggestions = async (req, res) => {
  try {
    const { q, limit = 10 } = req.query;

    if (!q || q.trim().length < 1) {
      return res.json({
        success: true,
        data: {
          suggestions: []
        }
      });
    }

    const searchTerm = q.trim();
    const suggestionLimit = Math.min(parseInt(limit), 20); // Cap at 20 suggestions

    // Get product name suggestions
    const productSuggestions = await Product.find({
      status: 'active',
      name: { $regex: searchTerm, $options: 'i' }
    })
    .select('name')
    .sort({ name: 1 })
    .limit(suggestionLimit)
    .lean();

    // Get category name suggestions
    const categorySuggestions = await Category.find({
      status: 'active',
      name: { $regex: searchTerm, $options: 'i' }
    })
    .select('name')
    .sort({ name: 1 })
    .limit(suggestionLimit)
    .lean();

    // Get vendor business name suggestions
    const vendorSuggestions = await Vendor.find({
      status: 'active',
      businessName: { $regex: searchTerm, $options: 'i' }
    })
    .select('businessName')
    .sort({ businessName: 1 })
    .limit(suggestionLimit)
    .lean();

    // Combine and format suggestions
    const suggestions = [];
    
    // Add product suggestions
    productSuggestions.forEach(product => {
      suggestions.push({
        text: product.name,
        type: 'product'
      });
    });

    // Add category suggestions
    categorySuggestions.forEach(category => {
      suggestions.push({
        text: category.name,
        type: 'category'
      });
    });

    // Add vendor suggestions
    vendorSuggestions.forEach(vendor => {
      suggestions.push({
        text: vendor.businessName,
        type: 'vendor'
      });
    });

    // Remove duplicates and limit results
    const uniqueSuggestions = suggestions
      .filter((suggestion, index, self) => 
        index === self.findIndex(s => s.text.toLowerCase() === suggestion.text.toLowerCase())
      )
      .slice(0, suggestionLimit);

    res.json({
      success: true,
      data: {
        suggestions: uniqueSuggestions
      }
    });

  } catch (error) {
    console.error('Get suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get search suggestions',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  searchProducts,
  searchCategories,
  searchVendors,
  globalSearch,
  getSuggestions
};