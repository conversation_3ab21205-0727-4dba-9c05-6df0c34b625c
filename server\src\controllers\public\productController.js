const productQueryService = require('../../services/public/productQueryService');
const specializedProductService = require('../../services/public/specializedProductService');
const { 
  successResponse, 
  errorResponse, 
  notFoundResponse, 
  badRequestResponse,
  paginatedResponse 
} = require('../../utils/helpers/responseHelper');
const { transformProductsPricing, transformProductPricing } = require('../../utils/helpers/pricingHelper');

/**
 * Get all active products for public viewing
 */
const getProducts = async (req, res) => {
  try {
    const result = await productQueryService.getProductsWithFilters(req.query);
    
    // Transform pricing based on user's preferred currency
    const userCurrency = req.userCurrency || 'INR';
    const transformedProducts = transformProductsPricing(result.products, userCurrency);
    
    return paginatedResponse(res, { products: transformedProducts }, result.pagination, 'Products retrieved successfully');
  } catch (error) {
    console.error('Get products error:', error);
    return errorResponse(res, 'Failed to fetch products', 500, error);
  }
};

/**
 * Get single product by ID or slug
 */
const getProduct = async (req, res) => {
  try {
    const { id } = req.params;
    
    const product = await productQueryService.getProductById(id);
    
    if (!product) {
      return notFoundResponse(res, 'Product not found');
    }

    // Transform pricing based on user's preferred currency
    const userCurrency = req.userCurrency || 'INR';
    const transformedProduct = transformProductPricing(product, userCurrency);

    // Get related products if category exists
    const relatedProducts = await productQueryService.getRelatedProducts(
      product._id, 
      product.category?._id, 
      8
    );
    const transformedRelatedProducts = transformProductsPricing(relatedProducts, userCurrency);

    return successResponse(res, { product: transformedProduct, relatedProducts: transformedRelatedProducts }, 'Product retrieved successfully');
  } catch (error) {
    console.error('Get product error:', error);
    return errorResponse(res, 'Failed to fetch product', 500, error);
  }
};

/**
 * Get featured products
 */
const getFeaturedProducts = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 8;
    
    const products = await specializedProductService.getFeaturedProducts(limit);
    
    // Transform pricing based on user's preferred currency
    const userCurrency = req.userCurrency || 'INR';
    const transformedProducts = transformProductsPricing(products, userCurrency);
    
    return successResponse(res, { products: transformedProducts }, 'Featured products retrieved successfully');
  } catch (error) {
    console.error('Get featured products error:', error);
    return errorResponse(res, 'Failed to fetch featured products', 500, error);
  }
};

/**
 * Get new arrivals
 */
const getNewArrivals = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 8;
    const days = parseInt(req.query.days) || 30;
    
    const products = await specializedProductService.getNewArrivals(limit, days);
    
    // Transform pricing based on user's preferred currency
    const userCurrency = req.userCurrency || 'INR';
    const transformedProducts = transformProductsPricing(products, userCurrency);
    
    return successResponse(res, { products: transformedProducts }, 'New arrivals retrieved successfully');
  } catch (error) {
    console.error('Get new arrivals error:', error);
    return errorResponse(res, 'Failed to fetch new arrivals', 500, error);
  }
};

/**
 * Get best selling products
 */
const getBestSelling = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 8;
    
    const products = await specializedProductService.getBestSellingProducts(limit);
    
    // Transform pricing based on user's preferred currency
    const userCurrency = req.userCurrency || 'INR';
    const transformedProducts = transformProductsPricing(products, userCurrency);
    
    return successResponse(res, { products: transformedProducts }, 'Best selling products retrieved successfully');
  } catch (error) {
    console.error('Get best selling products error:', error);
    return errorResponse(res, 'Failed to fetch best selling products', 500, error);
  }
};

/**
 * Get products by category
 */
const getProductsByCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    
    const result = await specializedProductService.getProductsByCategory(categoryId, req.query);
    
    // Transform pricing based on user's preferred currency
    const userCurrency = req.userCurrency || 'INR';
    if (result.products) {
      result.products = transformProductsPricing(result.products, userCurrency);
    }
    
    return paginatedResponse(res, result, result.pagination, 'Products by category retrieved successfully');
  } catch (error) {
    console.error('Get products by category error:', error);
    
    if (error.message === 'Category not found') {
      return notFoundResponse(res, error.message);
    }
    
    return errorResponse(res, 'Failed to fetch products by category', 500, error);
  }
};

/**
 * Get products by vendor
 */
const getProductsByVendor = async (req, res) => {
  try {
    const { vendorId } = req.params;
    
    const result = await specializedProductService.getProductsByVendor(vendorId, req.query);
    
    // Transform pricing based on user's preferred currency
    const userCurrency = req.userCurrency || 'INR';
    if (result.products) {
      result.products = transformProductsPricing(result.products, userCurrency);
    }
    
    return paginatedResponse(res, result, result.pagination, 'Products by vendor retrieved successfully');
  } catch (error) {
    console.error('Get products by vendor error:', error);
    
    if (error.message === 'Vendor not found') {
      return notFoundResponse(res, error.message);
    }
    
    return errorResponse(res, 'Failed to fetch products by vendor', 500, error);
  }
};

/**
 * Search products
 */
const searchProducts = async (req, res) => {
  try {
    const { q: query } = req.query;
    
    const result = await specializedProductService.searchProducts(query, req.query);
    
    // Transform pricing based on user's preferred currency
    const userCurrency = req.userCurrency || 'INR';
    if (result.products) {
      result.products = transformProductsPricing(result.products, userCurrency);
    }
    
    return paginatedResponse(res, result, result.pagination, 'Search results retrieved successfully');
  } catch (error) {
    console.error('Search products error:', error);
    
    if (error.message.includes('Search query must be')) {
      return badRequestResponse(res, error.message);
    }
    
    return errorResponse(res, 'Failed to search products', 500, error);
  }
};

module.exports = {
  getProducts,
  getProduct,
  getFeaturedProducts,
  getNewArrivals,
  getBestSelling,
  getProductsByCategory,
  getProductsByVendor,
  searchProducts
};
