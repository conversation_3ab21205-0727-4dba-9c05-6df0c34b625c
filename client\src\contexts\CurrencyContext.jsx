import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { sharedApi } from '../services/sharedApi';

const CurrencyContext = createContext();

export const useCurrency = () => {
  const context = useContext(CurrencyContext);
  if (!context) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};

export const CurrencyProvider = ({ children }) => {
  const [currentCurrency, setCurrentCurrency] = useState('INR'); // Default currency
  const [supportedCurrencies, setSupportedCurrencies] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch supported currencies from API
  const fetchSupportedCurrencies = useCallback(async () => {
    try {
      setLoading(true);
      const response = await sharedApi.getSupportedCurrencies();
      
      if (response.data.success) {
        const currencies = response.data.data.currencies || [];
        setSupportedCurrencies(currencies);
        
        // Set default currency if not already set
        if (!currentCurrency && response.data.data.defaultCurrency) {
          setCurrentCurrency(response.data.data.defaultCurrency);
        }
      }
    } catch (error) {
      console.error('Error fetching supported currencies:', error);
      // Fallback to basic currencies if API fails
      setSupportedCurrencies([
        { code: 'USD', name: 'US Dollar', symbol: '$' },
        { code: 'EUR', name: 'Euro', symbol: '€' },
        { code: 'GBP', name: 'British Pound', symbol: '£' },
        { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
        { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
        { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
        { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
        { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
        { code: 'SGD', name: 'Singapore Dollar', symbol: 'S$' },
        { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' }
      ]);
    } finally {
      setLoading(false);
    }
  }, [currentCurrency]);

  // Change currency
  const changeCurrency = useCallback(async (newCurrency) => {
    try {
      setLoading(true);
      
      // Update current currency in state
      setCurrentCurrency(newCurrency);
      
      // Try to update user preference if user is logged in
      try {
        const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');
        if (authToken) {
          await sharedApi.updateUserCurrency(newCurrency);
        }
      } catch (error) {
        // If user is not logged in or API fails, just store locally
        console.log('User not logged in or API failed, storing currency locally');
      }
      
      // Store in localStorage for persistence
      localStorage.setItem('selectedCurrency', newCurrency);
      
      message.success(`Currency changed to ${newCurrency}`);
    } catch (error) {
      console.error('Error changing currency:', error);
      message.error('Failed to change currency');
    } finally {
      setLoading(false);
    }
  }, []);

  // Get currency symbol
  const getCurrencySymbol = useCallback((currency = currentCurrency) => {
    const currencyInfo = supportedCurrencies.find(c => c.code === currency);
    return currencyInfo?.symbol || currency;
  }, [currentCurrency, supportedCurrencies]);

  // Get currency name
  const getCurrencyName = useCallback((currency = currentCurrency) => {
    const currencyInfo = supportedCurrencies.find(c => c.code === currency);
    return currencyInfo?.name || currency;
  }, [currentCurrency, supportedCurrencies]);

  // Filter products by current currency availability
  const filterProductsByCurrency = useCallback((products, currency = currentCurrency) => {
    if (!products || !Array.isArray(products)) return [];
    
    return products.filter(product => {
      // Check if product has multi-currency pricing
      if (product.multiCurrencyPricing && Array.isArray(product.multiCurrencyPricing)) {
        return product.multiCurrencyPricing.some(pricing => pricing.currency === currency);
      }
      
      // Fallback: if no multi-currency pricing, assume it's available in default currency (INR)
      return currency === 'INR';
    });
  }, [currentCurrency]);

  // Get product price in current currency
  const getProductPriceInCurrency = useCallback((product, currency = currentCurrency) => {
    // Check if product has multi-currency pricing
    if (product.multiCurrencyPricing && Array.isArray(product.multiCurrencyPricing)) {
      const currencyPricing = product.multiCurrencyPricing.find(pricing => pricing.currency === currency);
      if (currencyPricing) {
        return {
          basePrice: currencyPricing.basePrice,
          salePrice: currencyPricing.salePrice,
          currency: currencyPricing.currency
        };
      }
    }
    
    // Fallback to default pricing (assuming INR)
    if (currency === 'INR' && product.pricing) {
      return {
        basePrice: product.pricing.basePrice,
        salePrice: product.pricing.salePrice,
        currency: 'INR'
      };
    }
    
    return null;
  }, [currentCurrency]);

  // Initialize currency on mount
  useEffect(() => {
    const initializeCurrency = async () => {
      // First check localStorage
      const storedCurrency = localStorage.getItem('selectedCurrency');
      if (storedCurrency) {
        setCurrentCurrency(storedCurrency);
      }

      // Fetch supported currencies
      await fetchSupportedCurrencies();

      // Try to get user's preferred currency if logged in
      try {
        const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');
        if (authToken) {
          const response = await sharedApi.getUserCurrency();
          if (response.data.success && response.data.data.currency) {
            setCurrentCurrency(response.data.data.currency);
            localStorage.setItem('selectedCurrency', response.data.data.currency);
          }
        }
      } catch (error) {
        console.log('User not logged in or failed to get user currency preference');
      }
    };

    initializeCurrency();
  }, [fetchSupportedCurrencies]);

  const value = {
    // State
    currentCurrency,
    supportedCurrencies,
    loading,
    
    // Actions
    changeCurrency,
    getCurrencySymbol,
    getCurrencyName,
    fetchSupportedCurrencies,
    filterProductsByCurrency,
    getProductPriceInCurrency
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
};
